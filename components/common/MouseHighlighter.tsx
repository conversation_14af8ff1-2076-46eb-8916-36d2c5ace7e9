'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { cn } from '@/lib/utils';

interface MouseHighlighterProps {
  /** Whether the highlighter is enabled */
  enabled?: boolean;
  /** Size of the highlighter in pixels */
  size?: number;
  /** Custom CSS classes for styling */
  className?: string;
  /** Highlighter style variant */
  variant?: 'circle' | 'square' | 'glow' | 'spotlight' | 'line';
  /** Color theme */
  color?: 'primary' | 'secondary' | 'accent' | 'custom';
  /** Custom color (when color is 'custom') */
  customColor?: string;
  /** Opacity of the highlighter */
  opacity?: number;
  /** Animation duration in milliseconds */
  animationDuration?: number;
  /** Whether to show a trail effect */
  showTrail?: boolean;
  /** Z-index for positioning */
  zIndex?: number;
  /** Blend mode for the highlighter */
  blendMode?: 'normal' | 'multiply' | 'screen' | 'overlay' | 'difference' | 'exclusion';
  /** Line thickness for line variant */
  lineThickness?: number;
  /** Maximum number of line segments to keep */
  maxLineSegments?: number;
  /** Line fade duration in milliseconds */
  lineFadeDuration?: number;
}

interface MousePosition {
  x: number;
  y: number;
  timestamp?: number;
}

interface LineSegment {
  start: MousePosition;
  end: MousePosition;
  id: string;
  timestamp: number;
}

const MouseHighlighter: React.FC<MouseHighlighterProps> = ({
  enabled = true,
  size = 40,
  className,
  variant = 'circle',
  color = 'primary',
  customColor,
  opacity = 0.3,
  animationDuration = 100,
  showTrail = false,
  zIndex = 9999,
  blendMode = 'normal',
  lineThickness = 2,
  maxLineSegments = 30,
  lineFadeDuration = 2000,
}) => {
  const [mousePosition, setMousePosition] = useState<MousePosition>({ x: 0, y: 0 });
  const [isVisible, setIsVisible] = useState(false);
  const [trail, setTrail] = useState<MousePosition[]>([]);
  const [lineSegments, setLineSegments] = useState<LineSegment[]>([]);
  const [lastPosition, setLastPosition] = useState<MousePosition | null>(null);

  // Handle mouse movement
  const handleMouseMove = useCallback((event: MouseEvent) => {
    const newPosition = { x: event.clientX, y: event.clientY, timestamp: Date.now() };
    setMousePosition(newPosition);
    setIsVisible(true);

    // Create line segments for line variant
    if (variant === 'line' && lastPosition) {
      const distance = Math.sqrt(
        Math.pow(newPosition.x - lastPosition.x, 2) +
        Math.pow(newPosition.y - lastPosition.y, 2)
      );

      // Create shorter, smoother line segments with lower distance threshold
      if (distance > 1.5) {
        // Create multiple shorter segments for smoother lines
        const segmentCount = Math.max(1, Math.floor(distance / 6)); // Even shorter segments

        for (let i = 0; i < segmentCount; i++) {
          const ratio = (i + 1) / segmentCount;
          const segmentEnd = {
            x: lastPosition.x + (newPosition.x - lastPosition.x) * ratio,
            y: lastPosition.y + (newPosition.y - lastPosition.y) * ratio,
            timestamp: Date.now()
          };

          const segmentStart = i === 0 ? lastPosition : {
            x: lastPosition.x + (newPosition.x - lastPosition.x) * (i / segmentCount),
            y: lastPosition.y + (newPosition.y - lastPosition.y) * (i / segmentCount),
            timestamp: Date.now()
          };

          const newSegment: LineSegment = {
            start: segmentStart,
            end: segmentEnd,
            id: `line-${Date.now()}-${Math.random()}-${i}`,
            timestamp: Date.now()
          };

          setLineSegments(prev => {
            const updated = [newSegment, ...prev];
            // Keep only the most recent segments
            return updated.slice(0, maxLineSegments);
          });
        }

        setLastPosition(newPosition);
      }
    } else if (variant === 'line') {
      setLastPosition(newPosition);
    }

    // Add to trail if enabled (for other variants)
    if (showTrail && variant !== 'line') {
      setTrail(prev => {
        const newTrail = [newPosition, ...prev.slice(0, 4)]; // Keep last 5 positions
        return newTrail;
      });
    }
  }, [showTrail, variant, lastPosition, maxLineSegments]);

  // Handle mouse leave
  const handleMouseLeave = useCallback(() => {
    setIsVisible(false);
    if (showTrail) {
      setTrail([]);
    }
    if (variant === 'line') {
      setLastPosition(null);
    }
  }, [showTrail, variant]);

  // Set up event listeners
  useEffect(() => {
    if (!enabled) {
      setIsVisible(false);
      return;
    }

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [enabled, handleMouseMove, handleMouseLeave]);

  // Auto-fade line segments
  useEffect(() => {
    if (variant !== 'line' || lineSegments.length === 0) return;

    const interval = setInterval(() => {
      const now = Date.now();
      setLineSegments(prev =>
        prev.filter(segment => now - segment.timestamp < lineFadeDuration)
      );
    }, 100);

    return () => clearInterval(interval);
  }, [variant, lineSegments.length, lineFadeDuration]);

  // Don't render if disabled or not visible
  if (!enabled || !isVisible) {
    return null;
  }

  // Get color classes based on theme
  const getColorClasses = () => {
    switch (color) {
      case 'primary':
        return 'bg-primary-500 dark:bg-primary-400';
      case 'secondary':
        return 'bg-secondary-500 dark:bg-secondary-400';
      case 'accent':
        return 'bg-accent dark:bg-accent';
      case 'custom':
        return '';
      default:
        return 'bg-primary-500 dark:bg-primary-400';
    }
  };

  // Get variant-specific classes
  const getVariantClasses = () => {
    switch (variant) {
      case 'circle':
        return 'rounded-full';
      case 'square':
        return 'rounded-none';
      case 'glow':
        return 'rounded-full blur-sm';
      case 'spotlight':
        return 'rounded-full';
      case 'line':
        return 'rounded-full'; // For the cursor dot
      default:
        return 'rounded-full';
    }
  };

  // Get spotlight specific styles
  const getSpotlightStyles = () => {
    if (variant === 'spotlight') {
      return {
        background: `radial-gradient(circle, transparent 20%, rgba(0, 0, 0, ${opacity}) 70%)`,
        mixBlendMode: 'multiply' as const,
      };
    }
    return {};
  };

  // Base highlighter styles
  const highlighterStyles: React.CSSProperties = {
    left: mousePosition.x - size / 2,
    top: mousePosition.y - size / 2,
    width: size,
    height: size,
    opacity: variant === 'spotlight' ? 1 : opacity,
    transition: `all ${animationDuration}ms ease-out`,
    pointerEvents: 'none',
    zIndex,
    mixBlendMode: blendMode,
    backgroundColor: color === 'custom' && customColor ? customColor : undefined,
    ...getSpotlightStyles(),
  };

  // Trail styles
  const getTrailStyles = (index: number): React.CSSProperties => ({
    left: trail[index]?.x - (size * (1 - index * 0.2)) / 2,
    top: trail[index]?.y - (size * (1 - index * 0.2)) / 2,
    width: size * (1 - index * 0.2),
    height: size * (1 - index * 0.2),
    opacity: opacity * (1 - index * 0.3),
    transition: `all ${animationDuration * (1 + index)}ms ease-out`,
    pointerEvents: 'none',
    zIndex: zIndex - index - 1,
    mixBlendMode: blendMode,
    backgroundColor: color === 'custom' && customColor ? customColor : undefined,
  });

  return (
    <>
      {/* Line segments for line variant */}
      {variant === 'line' && lineSegments.map((segment) => {
        const length = Math.sqrt(
          Math.pow(segment.end.x - segment.start.x, 2) +
          Math.pow(segment.end.y - segment.start.y, 2)
        );
        const angle = Math.atan2(
          segment.end.y - segment.start.y,
          segment.end.x - segment.start.x
        ) * 180 / Math.PI;

        const age = Date.now() - segment.timestamp;
        const fadeOpacity = Math.max(0, 1 - (age / lineFadeDuration));

        return (
          <div
            key={segment.id}
            className={cn(
              'fixed origin-left',
              color !== 'custom' && getColorClasses(),
              className
            )}
            style={{
              left: segment.start.x,
              top: segment.start.y - lineThickness / 2,
              width: length,
              height: lineThickness,
              transform: `rotate(${angle}deg)`,
              opacity: fadeOpacity * opacity,
              backgroundColor: color === 'custom' && customColor ? customColor : undefined,
              zIndex,
              mixBlendMode: blendMode,
              pointerEvents: 'none',
              transition: `opacity ${animationDuration}ms ease-out`,
              borderRadius: `${lineThickness / 2}px`, // Rounded ends for smoother appearance
            }}
          />
        );
      })}

      {/* Trail effect */}
      {showTrail && variant !== 'line' && trail.map((position, index) => (
        <div
          key={`trail-${index}`}
          className={cn(
            'fixed transform -translate-x-1/2 -translate-y-1/2',
            getVariantClasses(),
            color !== 'custom' && getColorClasses(),
            className
          )}
          style={getTrailStyles(index)}
        />
      ))}

      {/* Main highlighter - smaller dot for line variant */}
      {variant !== 'line' && (
        <div
          className={cn(
            'fixed transform -translate-x-1/2 -translate-y-1/2',
            getVariantClasses(),
            color !== 'custom' && getColorClasses(),
            variant === 'glow' && 'shadow-2xl',
            className
          )}
          style={highlighterStyles}
        />
      )}

      {/* Small cursor dot for line variant */}
      {/* {variant === 'line' && (
        <div
          className={cn(
            'fixed transform -translate-x-1/2 -translate-y-1/2 rounded-full',
            color !== 'custom' && getColorClasses(),
            className
          )}
          style={{
            left: mousePosition.x,
            top: mousePosition.y,
            width: Math.max(3, size / 6),
            height: Math.max(3, size / 6),
            opacity: opacity * 1.2,
            backgroundColor: color === 'custom' && customColor ? customColor : undefined,
            zIndex: zIndex + 1,
            pointerEvents: 'none',
            transition: `all ${animationDuration / 2}ms ease-out`,
            borderRadius: '50%',
          }}
        />
      )} */}
    </>
  );
};

export default MouseHighlighter;
