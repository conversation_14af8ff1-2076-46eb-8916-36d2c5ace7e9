"use client"

import { useTranslation } from "react-i18next"
import { ServiceCard } from "@/components/common/ServiceCard"
import { Button } from "@/components/ui/button"
import { Icon } from "@/components/common/Icon"

export function WhatWeOffer() {
  const { t } = useTranslation()

  const services = [
    {
      key: "trading",
      title: t("services.trading.title"),
      description: t("services.trading.description"),
      image: "/images/services/trading.jpg" // We'll create placeholder images
    },
    {
      key: "logistics",
      title: t("services.logistics.title"),
      description: t("services.logistics.description"),
      image: "/images/services/logistics.jpg"
    },
    {
      key: "bullion",
      title: t("services.bullion.title"),
      description: t("services.bullion.description"),
      image: "/images/services/bullion.jpg"
    },
    {
      key: "jewelry",
      title: t("services.jewelry.title"),
      description: t("services.jewelry.description"),
      image: "/images/services/jewelry.jpg"
    }
  ]

  const handleServiceClick = (serviceKey: string) => {
    console.log(`Clicked on ${serviceKey} service`)
    // Add navigation logic here
  }

  const handleShowMoreProducts = () => {
    console.log("Show more products clicked")
    // Add navigation to products page
  }

  return (
    <section className="py-16 ">
      <div className="container mx-auto">
        <h2 className="text-[18px]font-bold text-primary-500  mb-4">
          {t("services.services")}
        </h2>
        <div className="flex justify-between items-center">
          <div className="text-start mb-12">
            <h2 className="text-3xl md:text-6xl font-bold text-secondary-500 dark:text-white-500 mb-4">
              {t("services.title")}
            </h2>
            <p className="text-lg text-secondary-500 dark:text-white-500 max-w-2xl">
              {t("services.subtitle")}
            </p>
          </div>
          <div className="text-end">
            <Button
              onClick={handleShowMoreProducts}
              className="bg-button-background-primary border border-button-background-primary-disable rounded-none hover:bg-button-background-primary  text-white-50 px-5 py-3 font-medium transition-colors duration-200 flex items-center gap-2"
            >
              {t("services.show_more_products")}
              <Icon name="arrow-right" size={16} />
            </Button>
          </div>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {services.map((service) => (
            <ServiceCard
              key={service.key}
              title={service.title}
              description={service.description}
              image={service.image}
              onClick={() => handleServiceClick(service.key)}
              className="h-full"
            />
          ))}
        </div>
      </div>
    </section>
  )
}
