"use client"

import React from "react"
import { Icon } from "@/components/common/Icon"

export default function Banner() {
  return (
    <div className="relative w-full h-screen overflow-hidden">
      {/* Background Video */}
      <video
        className="absolute inset-0 w-full h-full object-cover"
        autoPlay
        muted
        loop
        playsInline
        poster="/images/jewelry-banner-poster.jpg"
      >
        <source src="/videos/jewelry-banner.mp4" type="video/mp4" />
        <source src="/videos/jewelry-banner.webm" type="video/webm" />
      </video>

      {/* Animated background pattern as additional fallback */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse" />
        <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_50%,rgba(255,215,0,0.1),transparent_50%)]" />
      </div>

      {/* Dark overlay for better text readability */}
      <div className="absolute inset-0 bg-black/28" />

      {/* Content Container */}
      <div className="relative z-10 flex items-end justify-center h-full px-4 sm:px-6 lg:px-8">
        <div className="container">
          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 mb-8 items-center min-h-[17.5vh]">
            {/* First Text Box */}
            <h1 className="justify-start text-primary-50 text-5xl font-normal  leading-[56px]">
              Elegant Pure Jewelry With Quality Materials
            </h1>

            {/* Second Text Box */}
            <div>
              <p className="text-lg sm:text-xl text-primary-50 leading-relaxed mb-8">
                When an unknown printer took a galley of type and scramble it to
                make a type specimen book. It has survived not only centuries,
                but also the leap into electronic
              </p>

              {/* Get Started Button */}
              <div className="flex items-center gap-1">
                <p className="text-lg sm:text-xl text-primary-300">
                  Get Started
                </p>
                <Icon name="arrow-right"  size={20} className="ml-2 text-primary-300" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
