"use client"

import { useTranslation } from "react-i18next"
import { Icon } from "@/components/common/Icon"
import Logo from "@/components/common/logo"

export function Footer() {
  const { t } = useTranslation()

  const quickLinks = [
    { key: "about_us", href: "#about" },
    { key: "products", href: "#products" },
    { key: "services", href: "#services" },
    { key: "market_insights", href: "#market-insights" },
    { key: "blog", href: "#blog" },
    { key: "contact", href: "#contact" }
  ]

  const legalLinks = [
    { key: "terms_of_use", href: "#terms" },
    { key: "privacy_policy", href: "#privacy" },
    { key: "security_policy", href: "#security" },
    { key: "cookie_settings", href: "#cookies" }
  ]

  const socialLinks = [
    { name: "snapchat", href: "#", icon: "snapchat" },
    { name: "twitter", href: "#", icon: "x" },
    { name: "instagram", href: "#", icon: "instagram" },
    { name: "facebook", href: "#", icon: "facebook" }
  ]

  return (
    <footer className="bg-secondary-600 text-white">
      <div className="container mx-auto pt-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          {/* Company Info - Left Column (4 columns) */}
          <div className="lg:col-span-5">
            {/* Logo */}
            <div className="mb-6">
              <Logo width={140} height={45} className="mb-6" />
            </div>

            {/* Company Description */}
            <p className="text-gray-600 text-lg mb-8 font-normal font-body">
              {t("footer.company_description")}
            </p>

            {/* Social Media Icons */}
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <a
                  key={social.name}
                  href={social.href}
                  className="text-primary-600 hover:text-primary-400 p-1 bg-blur border border-button-icon-border rounded-sm transition-colors duration-200"
                  aria-label={social.name}
                >
                  <Icon name={social.icon as any} size={20} />
                </a>
              ))}
            </div>
          </div>

          {/* Quick Links - Middle Column (3 columns) */}
          <div className="lg:col-span-4 lg:ml-8">
            <h3 className="text-white font-semibold text-xl mb-6">
              {t("footer.quick_links")}
            </h3>
            <div className="grid grid-cols-2">

            <ul className="space-y-4">
              {quickLinks.slice(0, 4).map((link) => (
                <li key={link.key}>
                  <a
                    href={link.href}
                    className="text-gray-600 hover:text-white transition-colors duration-200 text-lg"
                    >
                    {t(`footer.${link.key}`)}
                  </a>
                </li>
              ))}
            </ul>
            <ul className="space-y-4">
              {quickLinks.slice(4, 6).map((link) => (
                <li key={link.key} >
                  <a
                    href={link.href}
                    className="text-gray-600 hover:text-white transition-colors duration-200 text-lg"
                    >
                    {t(`footer.${link.key}`)}
                  </a>
                </li>
              ))}
            </ul>
              </div>
          </div>

          {/* Legal - Right Column (3 columns) */}
          <div className="lg:col-span-3">
            <h3 className="text-white font-semibold text-lg mb-6">
              {t("footer.legal")}
            </h3>
            <ul className="space-y-4">
              {legalLinks.map((link) => (
                <li key={link.key}>
                  <a
                    href={link.href}
                    className="text-gray-600 hover:text-white transition-colors duration-200 text-lg"
                  >
                    {t(`footer.${link.key}`)}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Copyright */}
        <div className="mt-16 pt-8 border-t border-secondary-500">
          <div className="text-center">
            <p className="text-gray-400 text-md tracking-wider">
              {t("footer.copyright")}
            </p>
          </div>
        </div>
      </div>
    </footer>
  )
}
